"use client";

import { useFindAllInspectionForm } from "@/modules/inspection/hooks/form/list/find-all.hook";
import { Pagination } from "@/shared/components/custom/pagination";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/shadcn/table";
import { flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import * as React from "react";
import { columns } from "./columns";

interface IFormulariosTabProps {
	searchTerm?: string;
}

export const FormulariosTab: React.FC<IFormulariosTabProps> = ({ searchTerm }) => {
	const [rowSelection, setRowSelection] = React.useState({});
	const [currentPage, setCurrentPage] = React.useState(1);
	const [pageSize, setPageSize] = React.useState(10);

	const { data, pagination, isLoading, hasError, error } = useFindAllInspectionForm({
		page: currentPage,
		limit: pageSize,
		search: searchTerm || "",
	});

	const table = useReactTable({
		data,
		columns,
		state: { rowSelection },
		onRowSelectionChange: setRowSelection,
		enableRowSelection: true,
		getCoreRowModel: getCoreRowModel(),
		manualPagination: true,
		pageCount: pagination?.totalPages ?? 0,
	});

	const selectedCount = table.getFilteredSelectedRowModel().rows.length;

	return (
		<div className="space-y-4">
			<div className="overflow-x-auto rounded-lg border bg-background">
				<Table>
					<TableHeader className="bg-muted sticky top-0 z-10">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map(header => (
									<TableHead key={header.id} colSpan={header.colSpan} className="whitespace-nowrap font-semibold">
										{header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					<TableBody>
						{hasError ? (
							<TableRow>
								<TableCell colSpan={columns.length} className="h-24 text-center text-red-500">
									Erro ao carregar os dados: {error || "Ocorreu um erro desconhecido."}
								</TableCell>
							</TableRow>
						) : isLoading ? (
							<TableRow>
								<TableCell colSpan={columns.length} className="h-24 text-center">
									Carregando...
								</TableCell>
							</TableRow>
						) : table.getRowModel().rows.length ? (
							table.getRowModel().rows.map((row, idx) => (
								<TableRow
									key={row.id}
									data-state={row.getIsSelected() && "selected"}
									className={`${idx % 2 === 0 ? "bg-muted/20" : "bg-background"} transition-colors hover:bg-primary/10`}
								>
									{row.getVisibleCells().map(cell => (
										<TableCell
											key={cell.id}
											className="h-[28px] max-w-[260px] overflow-hidden text-ellipsis whitespace-nowrap"
											title={String(cell.getValue() ?? "")}
										>
											{flexRender(cell.column.columnDef.cell, cell.getContext())}
										</TableCell>
									))}
								</TableRow>
							))
						) : (
							<TableRow>
								<TableCell colSpan={columns.length} className="h-24 text-center">
									Nenhum resultado encontrado.
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>
			</div>
			{pagination && (
				<Pagination
					currentPage={pagination.currentPage}
					totalPages={pagination.totalPages}
					pageSize={pagination.itemsPerPage}
					totalItems={pagination.totalItems}
					selectedCount={selectedCount}
					onPageChange={setCurrentPage}
					onPageSizeChange={size => {
						setPageSize(size);
						setCurrentPage(1);
					}}
					showPageSizeSelector
					showSelectedInfo
				/>
			)}
		</div>
	);
};
