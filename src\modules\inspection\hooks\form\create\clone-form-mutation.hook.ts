import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation } from "@tanstack/react-query";

export const useCloneFormMutation = () => {
	const cloneFormMutation = useMutation({
		mutationKey: ["clone-inspection-form"],
		mutationFn: async (id: string) => {
			const res = await createPostRequest<ApiResponse<IMessageGlobalReturn>>(INSPECTION_FORM_ENDPOINTS.CLONE(id));
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
	});

	return {
		cloneForm: (id: string) =>
			toast.promise(cloneFormMutation.mutateAsync(id), {
				loading: "Clonando formulário...",
				success: "Formulário clonado com sucesso!",
				error: "Erro ao clonar formulário.",
			}),
	};
};
