import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation } from "@tanstack/react-query";

export const useUpdateFieldMutation = () => {
	const deleteFieldMutation = useMutation({
		mutationKey: ["delete-inspection-field"],
		mutationFn: async ({ formId, fieldId }: { formId: string; fieldId: string }) => {
			const res = await createPostRequest<ApiResponse<IMessageGlobalReturn>>(INSPECTION_FORM_ENDPOINTS.DELETE_FIELD(formId, fieldId));
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
	});

	return {
		deleteField: (variables: { formId: string; fieldId: string }) =>
			toast.promise(deleteFieldMutation.mutateAsync(variables), {
				loading: "Excluindo campo...",
				success: "Campo excluído com sucesso!",
				error: "Erro ao excluir campo.",
			}),
	};
};
