import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { ICreateFormDTO } from "@/modules/inspection/types/forms/create-form.dto";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation } from "@tanstack/react-query";

export const useCreateFormMutation = () => {
	const createFormMutation = useMutation({
		mutationKey: ["create-inspection-form"],
		mutationFn: async (form: ICreateFormDTO) => {
			const res = await createPostRequest<ApiResponse<IMessageGlobalReturn>>(INSPECTION_FORM_ENDPOINTS.CREATE, form);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
	});

	return {
		createForm: (form: ICreateFormDTO) =>
			toast.promise(createFormMutation.mutateAsync(form), {
				loading: "Criando formulário...",
				success: "Formulário criado com sucesso!",
				error: "Erro ao criar formulário.",
			}),
	};
};
