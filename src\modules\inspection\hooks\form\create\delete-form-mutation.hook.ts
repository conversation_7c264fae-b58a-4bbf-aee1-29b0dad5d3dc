import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation } from "@tanstack/react-query";

export const useDeleteFormMutation = () => {
	const deleteFormMutation = useMutation({
		mutationKey: ["delete-inspection-form"],
		mutationFn: async (id: string) => {
			const res = await createPostRequest<ApiResponse<IMessageGlobalReturn>>(INSPECTION_FORM_ENDPOINTS.DELETE(id));
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
	});

	return {
		deleteForm: (id: string) =>
			toast.promise(deleteFormMutation.mutateAsync(id), {
				loading: "Excluindo formulário...",
				success: "Formulário excluído com sucesso!",
				error: "Erro ao excluir formulário.",
			}),
	};
};
