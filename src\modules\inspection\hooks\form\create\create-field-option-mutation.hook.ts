import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { ICreateFieldOptionDto } from "@/modules/inspection/types/forms/create-field-option.dto";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation } from "@tanstack/react-query";

export const useCreateFieldOptionMutation = () => {
	const createFieldOptionMutation = useMutation({
		mutationKey: ["create-inspection-field-option"],
		mutationFn: async ({ formId, fieldId, data }: { formId: string; fieldId: string; data: ICreateFieldOptionDto }) => {
			const res = await createPostRequest<ApiResponse<IMessageGlobalReturn>>(
				INSPECTION_FORM_ENDPOINTS.CREATE_FIELD_OPTION(formId, fieldId),
				data
			);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
	});

	return {
		createFieldOption: (variables: { formId: string; fieldId: string; data: ICreateFieldOptionDto }) =>
			toast.promise(createFieldOptionMutation.mutateAsync(variables), {
				loading: "Criando opção de campo...",
				success: "Opção de campo criada com sucesso!",
				error: "Erro ao criar opção de campo.",
			}),
	};
};
