import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { IUpdateFieldDto } from "@/modules/inspection/types/forms/update-field.dto";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation } from "@tanstack/react-query";

export const useUpdateFieldMutation = () => {
	const updateFieldMutation = useMutation({
		mutationKey: ["update-inspection-field"],
		mutationFn: async ({ data, formId, fieldId }: { data: IUpdateFieldDto; formId: string; fieldId: string }) => {
			const res = await createPostRequest<ApiResponse<IMessageGlobalReturn>>(INSPECTION_FORM_ENDPOINTS.UPDATE_FIELD(formId, fieldId), data);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
	});

	return {
		updateField: (variables: { data: IUpdateFieldDto; formId: string; fieldId: string }) =>
			toast.promise(updateFieldMutation.mutateAsync(variables), {
				loading: "Atualizando campo...",
				success: "Campo atualizado com sucesso!",
				error: "Erro ao atualizar campo.",
			}),
	};
};
