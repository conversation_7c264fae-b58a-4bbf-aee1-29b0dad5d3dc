import z from "zod";
import { InspectionFormTypeEnum } from "../../constants/form/type-enum";

export const fieldTableSchema = z
	.object({
		fieldId: z.number().optional(),
		nickname: z.string().min(1, "Apelido é obrigatório"),
		required: z.boolean().optional(),
		group: z.number(),
		sequence: z.number(),
		typeId: z.nativeEnum(InspectionFormTypeEnum),
		measureId: z.number().min(1, "Medida é obrigatória"),
		groupTitle: z.string().optional(),
		biFilter: z.boolean().optional(),
		options: z
			.array(
				z.object({
					sequence: z.number().optional(),
					option: z.string().min(1, "Opção é obrigatória"),
				})
			)
			.optional(),
		tempId: z.string().optional(),
	})
	.refine(
		data => {
			if (data.typeId === InspectionFormTypeEnum.OPTIONS) {
				return data.options && data.options.length > 0;
			}
			return true;
		},
		{
			message: "Opções são obrigatórias para campos do tipo 'OPTIONS'",
			path: ["options"],
		}
	);

export type IFieldTable = z.infer<typeof fieldTableSchema>;

export const createFormSchema = z.object({
	title: z.string().min(1, "O título é obrigatório"),
	text: z.string().optional(),
	nomenclature: z.string().min(1, "A nomenclatura é obrigatória"),
	developerId: z.string().min(1, "O elaborador é obrigatório"),
	approverId: z.string().min(1, "O aprovador é obrigatório"),
	fields: fieldTableSchema.array().min(1, "Pelo menos um campo é obrigatório"),
});

export type ICreateForm = z.infer<typeof createFormSchema>;
