import { IInspectionForm } from "@/modules/inspection/hooks/form/list/find-all.hook";
import { Badge } from "@/shared/components/shadcn/badge";
import { Checkbox } from "@/shared/components/shadcn/checkbox";
import { ColumnDef } from "@tanstack/react-table";

export const columns: ColumnDef<IInspectionForm>[] = [
	{
		id: "select",
		header: ({ table }) => (
			<div className="flex items-center justify-start pl-2">
				<Checkbox
					checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && "indeterminate")}
					onCheckedChange={value => table.toggleAllPageRowsSelected(!!value)}
					aria-label="Selecionar todos"
				/>
			</div>
		),
		cell: ({ row }) => (
			<div className="flex items-center justify-start pl-2">
				<Checkbox checked={row.getIsSelected()} onCheckedChange={value => row.toggleSelected(!!value)} aria-label="Selecionar linha" />
			</div>
		),
		enableSorting: false,
		enableHiding: false,
		size: 10,
	},
	{
		accessorKey: "titulo",
		header: () => <div className="text-start font-semibold">Título</div>,
		cell: ({ row }) => (
			<div className="text-start">
				<span className="font-medium text-primary truncate max-w-[200px] block ">{row.original.title}</span>
			</div>
		),
	},
	{
		accessorKey: "nomenclatura",
		header: () => <div className="text-center font-semibold">Nomenclatura</div>,
		cell: ({ row }) => (
			<div className="flex justify-center">
				<Badge variant="outline" className="bg-muted-foreground/10 text-xs px-3 py-1 rounded">
					{row.original.nomenclature}
				</Badge>
			</div>
		),
	},
	{
		accessorKey: "revisao",
		header: () => <div className="text-center font-semibold">Revisão</div>,
		cell: ({ row }) => (
			<div className="text-center">
				<span className="text-muted-foreground text-sm font-medium">{row.original.revision}</span>
			</div>
		),
	},
	{
		id: "actions",
		header: () => <div className="text-right font-semibold pr-2">Ações</div>,
		cell: () => (
			<div className="text-right pr-2">
				<span className="text-xs text-muted-foreground">Ações</span>
			</div>
		),
		enableSorting: false,
		enableHiding: false,
		size: 80,
	},
];
