import { buildQueryParams } from "@/shared/lib/utils/url-query-params";
import { IFindAllInspectionFormParams } from "../hooks/form/list/find-all.hook";

export const INSPECTION_FORM_ENDPOINTS = {
	CREATE: "/inspection/form/create",
	FIND_ALL: (params: IFindAllInspectionFormParams) =>
		buildQueryParams("/inspection/form", {
			...params,
		}),
	UPDATE: (id: string) => `/inspection/form/${id}`, // patch
	DELETE: (id: string) => `/inspection/form/${id}`, // delete
	CLONE: (id: string) => `/inspection/form/${id}/clone`, // post
	FIND_BY_ID: (id: string) => `/inspection/form/${id}`, // get
	CREATE_FIELD: (formId: string) => `/inspection/form/${formId}/field`, // post
	UPDATE_FIELD: (formId: string, fieldId: string) => `/inspection/form/${formId}/field/${fieldId}`, // patch
	DELETE_FIELD: (formId: string, fieldId: string) => `/inspection/form/${formId}/field/${fieldId}`, // delete
	CREATE_FIELD_OPTION: (formId: string, fieldId: string) => `/inspection/form/${formId}/field/${fieldId}/option`, // post
	DELETE_FIELD_OPTION: (formId: string, fieldId: string, fieldOptionId: string) =>
		`/inspection/form/${formId}/field/${fieldId}/option/${fieldOptionId}`, // delete
};
