import { toast } from "@/core/toast";
import { INSPECTION_FORM_ENDPOINTS } from "@/modules/inspection/api/endpoints";
import { ICreateFieldDto } from "@/modules/inspection/types/forms/create-field.dto";
import { createPostRequest } from "@/shared/lib/requests";
import { IMessageGlobalReturn } from "@/shared/types/requests/message.type";
import { ApiResponse } from "@/shared/types/requests/request.type";
import { useMutation } from "@tanstack/react-query";

export const useCreateFieldMutation = () => {
	const createFieldMutation = useMutation({
		mutationKey: ["create-inspection-field"],
		mutationFn: async ({ data, id }: { data: ICreateFieldDto; id: string }) => {
			const res = await createPostRequest<ApiResponse<IMessageGlobalReturn>>(INSPECTION_FORM_ENDPOINTS.CREATE_FIELD(id), data);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
	});

	return {
		createField: (variables: { data: ICreateFieldDto; id: string }) =>
			toast.promise(createFieldMutation.mutateAsync(variables), {
				loading: "Criando campo...",
				success: "Campo criado com sucesso!",
				error: "Erro ao criar campo.",
			}),
	};
};
